import { v4 as uuidv4 } from "uuid";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";

/**
 * Generates a FHIR FamilyMemberHistory resource
 * @param {Object} familyHistory - Family history data from the database
 * @param {Object} patientResource - Patient FHIR resource
 * @returns {Object} FHIR FamilyMemberHistory resource
 */
export const generateFamilyMemberHistoryResource = async (
  familyHistory,
  patientResource
) => {
  const id = uuidv4();

  // Get SNOMED CT code for the condition
  const getSnomedData = await generateSnomedCtCode(familyHistory.name);

  // Map relationship types - this could be enhanced to be dynamic based on actual relationship data
  const getRelationshipCode = (conditionName) => {
    // Default to "family member" if no specific relationship is provided
    // In future, this could be enhanced to parse relationship from familyHistory.notes or add relationship field
    return {
      system: "http://snomed.info/sct",
      code: "444018008", // Family member
      display: "Person with characteristic related to subject of record"
    };
  };

  const relationshipCoding = getRelationshipCode(familyHistory.name);

  return {
    fullUrl: `urn:uuid:${id}`,
    resource: {
      resourceType: "FamilyMemberHistory",
      id,
      meta: {
        versionId: "1",
        lastUpdated: new Date().toISOString(),
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/FamilyMemberHistory"
        ]
      },
      patient: {
        reference: `urn:uuid:${patientResource.resource.id}`,
        type: "Patient",
        display: patientResource.resource.name?.[0]?.text || "Patient"
      },
      relationship: {
        coding: [relationshipCoding],
        text: relationshipCoding.display
      },
      status: "completed",
      condition: [
        {
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: getSnomedData.conceptId,
                display: getSnomedData.term
              }
            ],
            text: getSnomedData.term
          },
          ...(familyHistory.notes && {
            note: [
              {
                text: familyHistory.notes
              }
            ]
          }),
          ...(familyHistory.duration && familyHistory.duration.value && {
            onsetAge: {
              value: familyHistory.duration.value,
              unit: familyHistory.duration.unit || "years",
              system: "http://unitsofmeasure.org",
              code: familyHistory.duration.unit === "months" ? "mo" :
                    familyHistory.duration.unit === "days" ? "d" : "a"
            }
          })
        }
      ]
    }
  };
};

/**
 * Enhanced relationship mapping function for future use
 * This can be expanded when relationship data is available
 */
export const getRelationshipMapping = (relationshipType) => {
  const relationshipMap = {
    "father": { code: "66839005", display: "Father" },
    "mother": { code: "72705000", display: "Mother" },
    "brother": { code: "70924004", display: "Brother" },
    "sister": { code: "27733009", display: "Sister" },
    "grandfather": { code: "77560008", display: "Grandfather" },
    "grandmother": { code: "25656006", display: "Grandmother" },
    "uncle": { code: "38048003", display: "Uncle" },
    "aunt": { code: "87178005", display: "Aunt" },
    "cousin": { code: "421850000", display: "Cousin" },
    "family_member": { code: "444018008", display: "Family member" }
  };

  return relationshipMap[relationshipType?.toLowerCase()] || relationshipMap["family_member"];
};