import { v4 as uuidv4 } from "uuid";
import {documentReferenceMetadata, getSnomedCtCode, documentReferenceDiv } from "../../../utils/fhir.constants.js";
import { toTitleCase } from "../../../utils/titlecase.generator.js";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";

export const generateDocumentReferenceResource = async (status, docStatus, type, content, patientResource) => {
    const id = uuidv4();
    const getSnomedData = await generateSnomedCtCode(type);
    console.log("getSnomedData", getSnomedData);
    console.log("type", getSnomedCtCode(getSnomedData.conceptId, getSnomedData.term));
    return {
        fullUrl: `urn:uuid:${id}`,
        resource: {
            resourceType: 'DocumentReference',
            id,
            meta: documentReferenceMetadata(),
            status,
            docStatus,
            type: getSnomedCtCode(getSnomedData.conceptId, getSnomedData.term),
            subject: {
                reference: `urn:uuid:${patientResource.resource.id}`,
                display: patientResource.resource.resourceType
            },
            content: content.map(({ attachment }) => ({
                attachment: {
                    contentType: attachment.contentType,
                    language: 'en',
                    data: attachment.data,
                    title: toTitleCase(attachment.title.trim()),
                    creation: attachment.creation
                }
            }))
            // text: documentReferenceDiv()
        }
    }
}