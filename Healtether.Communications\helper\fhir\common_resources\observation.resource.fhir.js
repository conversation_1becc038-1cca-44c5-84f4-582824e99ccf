import { v4 as uuidv4 } from "uuid";
import {
  observationMetadata,
  observationCategory,
  observationDiv,
} from "../../../utils/fhir.constants.js";

export const generateObservationResource = async (
  observation,
  currentTime,
  patientResource,
  doctorNames, // doctorNames can be an array,
  practitionerResources,
  organizationResource,
) => {
  const id = uuidv4();

  // Ensure practitionerResources is an array
  const practitioners = Array.isArray(practitionerResources)
    ? practitionerResources
    : [];
   
  const normalize = (str) => (typeof str === "string" ? str.trim().toLowerCase() : "");

  // Ensure doctorNames is an array and normalize names
  const normalizedDoctorNames = Array.isArray(doctorNames)
    ? doctorNames.map(normalize)
    : [normalize(doctorNames)];

  // Find all matching practitioners
  const matchingPractitioners = practitioners.filter((practitioner) => {
    // Handle different practitioner structures
    const practitionerResource = practitioner.resource || practitioner;
    const practitionerName = practitionerResource?.name;

    if (!practitionerName || !Array.isArray(practitionerName)) {
      return false;
    }

    return practitionerName.some((nameObj) =>
      normalizedDoctorNames.includes(normalize(nameObj.text))
    );
  });

  // Build performer array including both practitioners and organization
  const performers = [];
  if (matchingPractitioners.length > 0) {
    performers.push(...matchingPractitioners.map((practitioner) => {
      const practitionerResource = practitioner.resource || practitioner;
      return {
        reference: `urn:uuid:${practitionerResource.id}`,
        type: "Practitioner",
        display: practitionerResource.name?.[0]?.text || "Practitioner"
      };
    }));
  }
  if (organizationResource) {
    performers.push({
      reference: `urn:uuid:${organizationResource.resource.id}`,
      type: "Organization",
      display: organizationResource.resource.name || "Organization"
    });
  }

  // Special handling for Blood Pressure Observations
  if (observation.bloodPressure) {
    return {
      fullUrl: `urn:uuid:${id}`,
      resource: {
        resourceType: "Observation",
        id,
        meta: observationMetadata(),
        status: "final",
        category: [observationCategory()],
        code: {
          coding: [
            {
              system: "http://loinc.org",
              code: "85354-9",
              display: "Blood pressure panel with all children optional",
            },
          ],
          text: "Blood pressure panel with all children optional",
        },
        subject: {
          reference: `urn:uuid:${patientResource.resource.id}`,
          type: "Patient",
          display: patientResource.resource.name?.[0]?.text || "Patient",
        },
        effectiveDateTime: observation.effectiveDateTime || currentTime,
        issued: new Date().toISOString(),
        performer: performers.length > 0 ? performers : undefined,
        component: [
          {
            code: {
              coding: [
                {
                  system: "http://loinc.org",
                  code: "8462-4",
                  display: "Diastolic blood pressure",
                },
              ],
              text: "Diastolic blood pressure",
            },
            valueQuantity: {
              value: observation.bloodPressure[1]?.valueQuantity?.value || 0,
              unit: "mmhg",
              system: "http://unitsofmeasure.org",
              code: "mm[Hg]",
            },
          },
          {
            code: {
              coding: [
                {
                  system: "http://loinc.org",
                  code: "8480-6",
                  display: "Systolic blood pressure",
                },
              ],
              text: "Systolic blood pressure",
            },
            valueQuantity: {
              value: observation.bloodPressure[0]?.valueQuantity?.value || 0,
              unit: "mmhg",
              system: "http://unitsofmeasure.org",
              code: "mm[Hg]",
            },
          },
        ],
        // text: observationDiv(),
      },
    };
  }

  // For other vital sign observations (e.g., heart rate, temperature, etc.)
  return {
    fullUrl: `urn:uuid:${id}`,
    resource: {
      resourceType: "Observation",
      id,
      meta: observationMetadata(),
      status: observation.status || "final",
      category: [observationCategory()],
      code: {
        coding: [
          {
            system: observation.code?.system || "http://loinc.org",
            code: observation.code.code,
            display: observation.code.display,
          },
        ],
        text: observation.code.display,
      },
      subject: {
        reference: `urn:uuid:${patientResource.resource.id}`,
        type: "Patient",
        display: patientResource.resource.name?.[0]?.text || "Patient",
      },
      effectiveDateTime: observation.effectiveDateTime || currentTime,
      issued: new Date().toISOString(),
      performer: performers.length > 0 ? performers : undefined,
      valueQuantity: observation.valueQuantity
        ? {
            value: observation.valueQuantity.value,
            unit: observation.valueQuantity.unit,
            system: "http://unitsofmeasure.org",
            code: observation.valueQuantity.code,
          }
        : undefined
      // text: observationDiv(),
    },
  };
};

/**
 * Generate multiple observation resources from vitals data
 * @param {Object} vitals - Vitals data from database
 * @param {String} currentTime - Current timestamp
 * @param {Object} patientResource - Patient FHIR resource
 * @param {Array} practitionerResources - Practitioner FHIR resources
 * @param {Object} organizationResource - Organization FHIR resource
 * @param {Array} doctorNames - Array of doctor names
 * @returns {Array} Array of FHIR Observation resources
 */
export const generateObservationResourcesFromVitals = async (
  vitals,
  currentTime,
  patientResource,
  practitionerResources,
  organizationResource,
  doctorNames
) => {
  const observations = [];

  if (!vitals) return observations;

  // Blood Pressure (special handling with components)
  if (vitals.bloodPressure?.systolic && vitals.bloodPressure?.diastolic) {
    const bpObservation = {
      bloodPressure: [
        {
          valueQuantity: { value: vitals.bloodPressure.systolic }
        },
        {
          valueQuantity: { value: vitals.bloodPressure.diastolic }
        }
      ],
      effectiveDateTime: currentTime
    };

    observations.push(await generateObservationResource(
      bpObservation,
      currentTime,
      patientResource,
      doctorNames,
      practitionerResources,
      organizationResource,
    ));
  }

  // Height
  if (vitals.height) {
    observations.push(await generateObservationResource(
      {
        status: "final",
        code: {
          system: "http://loinc.org",
          code: "8302-2",
          display: "Body height"
        },
        valueQuantity: {
          value: vitals.height,
          unit: "cm",
          code: "cm"
        },
        effectiveDateTime: currentTime
      },
      currentTime,
      patientResource,
      doctorNames,
      practitionerResources,
      organizationResource,
    ));
  }

  // Weight
  if (vitals.weight) {
    observations.push(await generateObservationResource(
      {
        status: "final",
        code: {
          system: "http://snomed.info/sct",
          code: "27113001",
          display: "Body weight"
        },
        valueQuantity: {
          value: vitals.weight,
          unit: "kg",
          code: "kg"
        },
        effectiveDateTime: currentTime
      },
      currentTime,
      patientResource,
      doctorNames,
      practitionerResources,
      organizationResource,
    ));
  }

  // Temperature
  if (vitals.temperature) {
    observations.push(await generateObservationResource(
      {
        status: "final",
        code: {
          system: "http://loinc.org",
          code: "61008-9",
          display: "Body surface temperature"
        },
        valueQuantity: {
          value: vitals.temperature,
          unit: "degF",
          code: "[degF]"
        },
        effectiveDateTime: currentTime
      },
      currentTime,
      patientResource,
      doctorNames,
      practitionerResources,
      organizationResource,
    ));
  }

  // Pulse Rate
  if (vitals.pulseRate) {
    observations.push(await generateObservationResource(
      {
        status: "final",
        code: {
          system: "http://snomed.info/sct",
          code: "78564009",
          display: "Pulse rate"
        },
        valueQuantity: {
          value: vitals.pulseRate,
          unit: "beats/min",
          code: "/min"
        },
        effectiveDateTime: currentTime
      },
      currentTime,
      patientResource,
      doctorNames,
      practitionerResources,
      organizationResource,
    ));
  }

  // Respiratory Rate
  if (vitals.respiratoryRate) {
    observations.push(await generateObservationResource(
      {
        status: "final",
        code: {
          system: "http://snomed.info/sct",
          code: "86290005",
          display: "Respiratory rate"
        },
        valueQuantity: {
          value: vitals.respiratoryRate,
          unit: "breaths/min",
          code: "/min"
        },
        effectiveDateTime: currentTime
      },
      currentTime,
      patientResource,
      doctorNames,
      practitionerResources,
      organizationResource,
    ));
  }

  // SPO2 (Oxygen Saturation)
  if (vitals.spo2) {
    observations.push(await generateObservationResource(
      {
        status: "final",
        code: {
          system: "http://snomed.info/sct",
          code: "250554003",
          display: "Measurement of oxygen saturation at periphery"
        },
        valueQuantity: {
          value: vitals.spo2,
          unit: "%",
          code: "%"
        },
        effectiveDateTime: currentTime
      },
      currentTime,
      patientResource,
      doctorNames,
      practitionerResources,
      organizationResource,
    ));
  }

  // RBS (Random Blood Sugar)
  if (vitals.rbs) {
    observations.push(await generateObservationResource(
      {
        status: "final",
        code: {
          system: "http://snomed.info/sct",
          code: "269864005",
          display: "Blood glucose result"
        },
        valueQuantity: {
          value: vitals.rbs,
          unit: "mg/dL",
          code: "mg/dL"
        },
        effectiveDateTime: currentTime
      },
      currentTime,
      patientResource,
      doctorNames,
      practitionerResources,
      organizationResource,
    ));
  }

  return observations;
};
