import { v4 as uuidv4 } from "uuid";

import { conditionMetadata, conditionClinicalStatus, getSnomedCtCode, conditionDiv } from "../../../utils/fhir.constants.js";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";
import { toTitleCase } from "../../../utils/titlecase.generator.js";
import portalDb from "../../../config/clinics.collections.config.js";
const Prescription = portalDb.model("Prescription");


export const generateConditionResource = async (condition, conditionStatus, patientResource,cond,patientId) => {
    const id = uuidv4();
    const getSnomedData = await generateSnomedCtCode(condition);
    const prescription = await Prescription.findOne({ patient: patientId }).sort({
      "created.on": -1,
    });
    const notes = `(Notes: ${prescription.diagnosis.filter(diag=>diag.name===getSnomedData.term).map(d => d.notes).join(" ")})`;



    return {
        fullUrl: `urn:uuid:${id}`,
        resource: {
            resourceType: 'Condition',
            id,
            meta: conditionMetadata(),
            clinicalStatus: conditionClinicalStatus(conditionStatus, toTitleCase(conditionStatus)),
            code: getSnomedCtCode(toTitleCase(getSnomedData.conceptId), toTitleCase(getSnomedData.term), notes),
            recordedDate:cond.recordedDate,
            onsetPeriod:{
                 start: cond.startDate,
                end: cond.endDate
            },
            subject: {
                reference: `urn:uuid:${patientResource.resource.id}`,
                display: patientResource.resource.resourceType
            }
            // text: conditionDiv()
        }
    }
}