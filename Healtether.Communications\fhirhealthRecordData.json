{"fhirId": "cdc50f99-c452-484e-9602-76bfa8084646", "general": {"artifact": "HealthDocumentRecord", "hipUrl": "https://www.healtether.com", "hipIds": ["hip1", "hip2"], "status": "final", "clientId": "SBX_003515", "_id": "683af57d210e79d2e024c10d"}, "patient": {"id": "683af4ee410474e62e2c6187", "abhaNumber": "91-7573-2448-7649", "abhaAddress": "testraja@sbx", "name": {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prefix": ["undefined"]}, "gender": "male", "dob": "1999-10-09", "doctors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "allergyIntolerances": [], "telecom": [{"system": "phone", "value": "**********", "use": "mobile", "_id": "683af57d210e79d2e024c10f"}], "address": [{"use": "home", "type": "physical", "text": "NO 12(2), VEE<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> STREET, THIRUMALAPURAM, BODINAYAKANUR, Bodinayackanur, Bodinayakanur, Theni, Tamil Nadu", "city": "THENI", "state": "TAMIL NADU", "district": "TAMIL NADU", "postalCode": "625513", "country": "india", "_id": "683af57d210e79d2e024c110"}], "_id": "683af57d210e79d2e024c10e"}, "practitioners": [{"names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "licenses": [{"code": "MD", "display": "Medical License number", "licNo": "1234567", "_id": "683af57d210e79d2e024c112"}], "patient": "patient123", "gender": "female", "birthDate": "2025-05-31", "address": [{"use": "home", "type": "physical", "text": "<PERSON><PERSON><PERSON> ganj", "postalCode": "474001", "country": "india", "_id": "683af57d210e79d2e024c113"}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile", "_id": "683af57d210e79d2e024c114"}], "_id": "683af57d210e79d2e024c111"}], "encounter": {"status": "finished", "startTime": "2025-05-31T12:24:46.043Z", "endTime": "2023-11-01T10:00:00+05:30", "_id": "683af57d210e79d2e024c115"}, "organization": {"name": "TEST", "telecom": [{"system": "phone", "value": "**********", "use": "work", "_id": "683af57d210e79d2e024c117"}], "licenses": [{"code": "PRN", "display": "Provider number", "licNo": "1234567", "_id": "683af57d210e79d2e024c118"}], "_id": "683af57d210e79d2e024c116"}, "documentReferences": [], "signature": {"who": {"type": "Practitioner", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sigFormat": "image/jpeg", "data": "c2lnbmF0dXJlIGRhdGEgaGVyZQ==", "_id": "683af57d210e79d2e024c119"}, "abhaCareContextLinked": false, "_id": "683af57d210e79d2e024c10c", "__v": 0}